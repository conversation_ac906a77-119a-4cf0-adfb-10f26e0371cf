<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'Dashboard') - PPDS RSK Dharmais</title>
    <meta name="description" content="Dashboard Presensi PPDS RS Kanker Dharmais">
    <meta name="keywords" content="PPDS, Presensi, RS Kanker Dharmais">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Favicons -->
    <link href="{{ asset('assets/img/dharmais_icon.png') }}" rel="icon">
    <link href="{{ asset('assets/img/dharmais_icon.png') }}" rel="apple-touch-icon">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Vendor CSS -->
    <link href="{{ asset('assets/vendor/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/vendor/bootstrap-icons/bootstrap-icons.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/vendor/aos/aos.css') }}" rel="stylesheet">

    <style>
        /* ===== MEDICAL THEME VARIABLES ===== */
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --primary-light: #3b82f6;
            --secondary-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;

            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --bg-dark: #0f172a;

            --text-primary: #0f172a;
            --text-secondary: #475569;
            --text-muted: #64748b;
            --text-light: #94a3b8;

            --border-color: #e2e8f0;
            --border-light: #f1f5f9;

            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);

            --sidebar-width: 280px;
            --header-height: 70px;

            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            --font-heading: 'Poppins', sans-serif;
        }

        /* ===== GLOBAL STYLES ===== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: var(--font-primary);
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* ===== LAYOUT STRUCTURE ===== */
        .dashboard-wrapper {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .dashboard-container {
            display: flex;
            flex: 1;
        }

        /* ===== HEADER STYLES ===== */
        .dashboard-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: var(--header-height);
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
            z-index: 1000;
            display: flex;
            align-items: center;
            padding: 0 1.5rem;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.25rem;
            padding: 0.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .sidebar-toggle:hover {
            background-color: var(--bg-tertiary);
            color: var(--primary-color);
        }

        .header-brand {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            text-decoration: none;
            color: var(--text-primary);
        }

        .header-brand img {
            height: 40px;
            width: auto;
        }

        .header-brand-text {
            font-family: var(--font-heading);
            font-weight: 600;
            font-size: 1.25rem;
            color: var(--primary-color);
        }

        .header-right {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* ===== SIDEBAR STYLES ===== */
        .dashboard-sidebar {
            position: fixed;
            top: var(--header-height);
            left: 0;
            width: var(--sidebar-width);
            height: calc(100vh - var(--header-height));
            background: var(--bg-primary);
            border-right: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
            z-index: 999;
            transform: translateX(0);
            transition: transform 0.3s ease;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .sidebar-hidden .dashboard-sidebar {
            transform: translateX(-100%);
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-light);
            text-align: center;
        }

        .sidebar-user {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.75rem;
        }

        .sidebar-avatar {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .sidebar-user-info h6 {
            font-family: var(--font-heading);
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            font-size: 0.95rem;
        }

        .sidebar-user-info p {
            color: var(--text-muted);
            margin: 0;
            font-size: 0.8rem;
        }

        .sidebar-nav {
            flex: 1;
            padding: 1rem 0;
        }

        .sidebar-nav-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-nav-item {
            margin: 0.25rem 0;
        }

        .sidebar-nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.875rem 1.5rem;
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.9rem;
            border-radius: 0.5rem;
            margin: 0 1rem;
            transition: all 0.2s ease;
            position: relative;
        }

        .sidebar-nav-link:hover {
            background-color: var(--bg-tertiary);
            color: var(--primary-color);
            transform: translateX(4px);
        }

        .sidebar-nav-link.active {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            box-shadow: var(--shadow-md);
        }

        .sidebar-nav-link.active::before {
            content: '';
            position: absolute;
            left: -1rem;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 20px;
            background: var(--primary-color);
            border-radius: 0 2px 2px 0;
        }

        .sidebar-nav-icon {
            font-size: 1.1rem;
            width: 20px;
            text-align: center;
        }

        .sidebar-nav-text {
            font-weight: 500;
        }

        .sidebar-footer {
            padding: 1.5rem;
            border-top: 1px solid var(--border-light);
            text-align: center;
        }

        .sidebar-footer-text {
            color: var(--text-muted);
            font-size: 0.8rem;
            line-height: 1.4;
        }

        .sidebar-footer-brand {
            color: var(--primary-color);
            font-weight: 600;
        }

        /* ===== MAIN CONTENT STYLES ===== */
        .dashboard-main {
            flex: 1;
            margin-left: var(--sidebar-width);
            margin-top: var(--header-height);
            transition: margin-left 0.3s ease;
            min-height: calc(100vh - var(--header-height));
            background: var(--bg-secondary);
        }

        .sidebar-hidden .dashboard-main {
            margin-left: 0;
        }

        .main-content {
            padding: 2rem;
            max-width: 100%;
        }

        .content-header {
            margin-bottom: 2rem;
        }

        .page-title {
            font-family: var(--font-heading);
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 0.5rem 0;
        }

        .page-subtitle {
            color: var(--text-muted);
            font-size: 0.95rem;
            margin: 0;
        }

        /* ===== CARD STYLES ===== */
        .dashboard-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            box-shadow: var(--shadow-sm);
            transition: all 0.2s ease;
            overflow: hidden;
        }

        .dashboard-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .card-header-custom {
            background: var(--bg-tertiary);
            border-bottom: 1px solid var(--border-color);
            padding: 1.25rem 1.5rem;
        }

        .card-title {
            font-family: var(--font-heading);
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            font-size: 1.1rem;
        }

        .card-body-custom {
            padding: 1.5rem;
        }

        /* ===== BUTTON STYLES ===== */
        .btn-primary-custom {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border: none;
            color: white;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
            box-shadow: var(--shadow-sm);
        }

        .btn-primary-custom:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
        }

        .btn-outline-custom {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            background: transparent;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-outline-custom:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-1px);
        }

        /* ===== RESPONSIVE DESIGN ===== */
        .sidebar-overlay {
            position: fixed;
            top: var(--header-height);
            left: 0;
            width: 100vw;
            height: calc(100vh - var(--header-height));
            background: rgba(0, 0, 0, 0.5);
            z-index: 998;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .sidebar-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        @media (max-width: 768px) {
            :root {
                --sidebar-width: 280px;
            }

            .dashboard-sidebar {
                transform: translateX(-100%);
            }

            .sidebar-open .dashboard-sidebar {
                transform: translateX(0);
            }

            .dashboard-main {
                margin-left: 0;
            }

            .main-content {
                padding: 1rem;
            }

            .header-brand-text {
                display: none;
            }

            /* Mobile-specific improvements for camera section */
            .camera-container {
                padding: 0.75rem;
            }

            #video {
                height: 250px;
                object-fit: cover;
            }

            .card-body-custom {
                padding: 1rem;
            }

            .card-header-custom {
                padding: 1rem;
            }

            /* Mobile table improvements */
            .table-responsive {
                border-radius: 0.75rem;
                overflow: hidden;
            }

            .table-custom {
                font-size: 0.85rem;
            }

            .table-custom th,
            .table-custom td {
                padding: 0.75rem 0.5rem;
                white-space: nowrap;
            }

            .status-badge {
                font-size: 0.7rem;
                padding: 0.25rem 0.5rem;
            }

            /* Mobile button improvements */
            .btn-outline-custom,
            .btn-primary-custom {
                padding: 0.6rem 1rem;
                font-size: 0.9rem;
            }

            /* Mobile card spacing */
            .dashboard-card {
                margin-bottom: 1rem;
            }

            /* Mobile page header */
            .page-title {
                font-size: 1.5rem;
            }

            .page-subtitle {
                font-size: 0.9rem;
            }
        }

        @media (min-width: 769px) {
            .sidebar-overlay {
                display: none !important;
            }
        }

        /* ===== SCROLLBAR STYLES ===== */
        .dashboard-sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .dashboard-sidebar::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
        }

        .dashboard-sidebar::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        .dashboard-sidebar::-webkit-scrollbar-thumb:hover {
            background: var(--text-muted);
        }

        /* ===== MOBILE-SPECIFIC IMPROVEMENTS ===== */
        @media (max-width: 576px) {
            /* Extra small devices - phones */
            .main-content {
                padding: 0.75rem;
            }

            .row {
                margin-left: -0.5rem;
                margin-right: -0.5rem;
            }

            .row > * {
                padding-left: 0.5rem;
                padding-right: 0.5rem;
            }

            /* Camera section mobile optimization */
            .camera-container {
                padding: 0.5rem;
                margin-bottom: 1rem;
            }

            #video {
                height: 200px;
                width: 100%;
            }

            .d-flex.gap-2.justify-content-center.flex-wrap {
                gap: 0.5rem !important;
            }

            .btn-outline-custom,
            .btn-primary-custom {
                padding: 0.5rem 0.75rem;
                font-size: 0.85rem;
                flex: 1;
                min-width: 120px;
            }

            /* Table mobile optimization */
            .table-custom {
                font-size: 0.8rem;
            }

            .table-custom th,
            .table-custom td {
                padding: 0.5rem 0.25rem;
                vertical-align: middle;
            }

            .table-custom th:first-child,
            .table-custom td:first-child {
                padding-left: 0.5rem;
            }

            .table-custom th:last-child,
            .table-custom td:last-child {
                padding-right: 0.5rem;
            }

            /* Status badges mobile */
            .status-badge {
                font-size: 0.65rem;
                padding: 0.2rem 0.4rem;
                display: inline-block;
                min-width: 50px;
                text-align: center;
            }

            /* Mobile card improvements */
            .card-title {
                font-size: 1rem;
            }

            .sidebar-avatar {
                width: 50px !important;
                height: 50px !important;
                font-size: 1.2rem !important;
            }

            /* Mobile page header */
            .content-header {
                margin-bottom: 1rem;
            }

            .page-title {
                font-size: 1.25rem;
                line-height: 1.3;
            }

            .page-subtitle {
                font-size: 0.85rem;
            }

            /* Hide some elements on very small screens */
            .d-none.d-md-block {
                display: none !important;
            }

            /* Mobile table scroll optimization */
            .table-responsive {
                -webkit-overflow-scrolling: touch;
                scrollbar-width: thin;
            }

            .table-responsive::-webkit-scrollbar {
                height: 4px;
            }

            .table-responsive::-webkit-scrollbar-track {
                background: var(--bg-tertiary);
            }

            .table-responsive::-webkit-scrollbar-thumb {
                background: var(--border-color);
                border-radius: 2px;
            }

            /* Mobile video container */
            .camera-container video {
                border-radius: 0.5rem;
            }

            /* Mobile alert styling */
            .alert {
                padding: 0.75rem;
                font-size: 0.9rem;
            }
        }

        /* ===== ADDITIONAL MOBILE IMPROVEMENTS ===== */
        @media (max-width: 480px) {
            /* Very small phones */
            .main-content {
                padding: 0.5rem;
            }

            .dashboard-card {
                border-radius: 0.5rem;
            }

            .card-header-custom {
                padding: 0.75rem;
            }

            .card-body-custom {
                padding: 0.75rem;
            }

            .card-title {
                font-size: 0.95rem;
            }

            .page-title {
                font-size: 1.1rem;
            }

            .btn-lg {
                padding: 0.6rem 1rem;
                font-size: 0.9rem;
            }

            /* Very compact table for tiny screens */
            .table-custom th,
            .table-custom td {
                padding: 0.4rem 0.2rem;
                font-size: 0.75rem;
            }

            .status-badge {
                font-size: 0.6rem;
                padding: 0.15rem 0.3rem;
                min-width: 40px;
            }
        }

        /* ===== UTILITY CLASSES ===== */
        .bg-gradient-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        }

        .text-primary-custom {
            color: var(--primary-color);
        }

        .text-muted-custom {
            color: var(--text-muted);
        }

        .border-custom {
            border: 1px solid var(--border-color);
        }

        .shadow-custom {
            box-shadow: var(--shadow-md);
        }

        .rounded-custom {
            border-radius: 0.75rem;
        }

        /* ===== MEDICAL SPECIFIC STYLES ===== */
        .status-badge {
            padding: 0.375rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .status-success {
            background: var(--success-color);
            color: white;
        }

        .status-warning {
            background: var(--warning-color);
            color: white;
        }

        .status-danger {
            background: var(--danger-color);
            color: white;
        }

        .status-info {
            background: var(--info-color);
            color: white;
        }

        /* ===== CAMERA & VIDEO STYLES ===== */
        #video {
            border: 3px solid var(--border-color);
            border-radius: 0.75rem;
            max-width: 100%;
            height: auto;
            background: var(--bg-tertiary);
        }

        .camera-container {
            background: var(--bg-primary);
            border-radius: 0.75rem;
            padding: 1rem;
            border: 1px solid var(--border-color);
        }

        /* ===== TABLE STYLES ===== */
        .table-custom {
            background: var(--bg-primary);
            border-radius: 0.75rem;
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .table-custom th {
            background: var(--bg-tertiary);
            border: none;
            font-weight: 600;
            color: var(--text-primary);
            padding: 1rem;
        }

        .table-custom td {
            border: none;
            padding: 1rem;
            border-bottom: 1px solid var(--border-light);
        }

        .table-custom tbody tr:last-child td {
            border-bottom: none;
        }

        /* ===== FOOTER STYLES ===== */
        .footer {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin-top: 3rem;
            padding: 2rem 0 1rem 0;
        }

        .footer a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .footer a:hover {
            color: white;
        }


    </style>
</head>

<body class="dashboard-wrapper">
    <!-- Header -->
    @include('layouts.partials.header')

    <!-- Dashboard Container -->
    <div class="dashboard-container">
        <!-- Sidebar -->
        @include('layouts.partials.sidebar')

        <!-- Main Content Area -->
        <main class="dashboard-main">
            <div class="main-content">
                <!-- Page Header -->
                @hasSection('page-header')
                    <div class="content-header">
                        @yield('page-header')
                    </div>
                @endif

                <!-- Main Content -->
                @yield('content')
            </div>

            <!-- Footer -->
            @include('layouts.partials.footer')
        </main>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mb-0">Sedang memproses presensi...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1100;">
        <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header bg-success text-white">
                <i class="bi bi-check-circle me-2"></i>
                <strong class="me-auto">Berhasil</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body"></div>
        </div>

        <div id="errorToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header bg-danger text-white">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <strong class="me-auto">Error</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body"></div>
        </div>
    </div>

    <!-- Vendor JS Files -->
    <script src="{{ asset('assets/vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/aos/aos.js') }}"></script>

    <!-- Main JS File -->
    <script src="{{ asset('assets/js/main.js') }}"></script>

    <script>
        // Dashboard Sidebar Toggle
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            const body = document.body;

            // Toggle sidebar
            function toggleSidebar() {
                if (window.innerWidth <= 768) {
                    // Mobile behavior
                    body.classList.toggle('sidebar-open');
                    sidebarOverlay.classList.toggle('show');
                } else {
                    // Desktop behavior
                    body.classList.toggle('sidebar-hidden');
                }
            }

            // Event listeners
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', toggleSidebar);
            }

            // Close sidebar when clicking overlay (mobile)
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', function() {
                    body.classList.remove('sidebar-open');
                    sidebarOverlay.classList.remove('show');
                });
            }

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    body.classList.remove('sidebar-open');
                    sidebarOverlay.classList.remove('show');
                } else {
                    body.classList.remove('sidebar-hidden');
                }
            });

            // Close sidebar when clicking nav links on mobile
            const navLinks = document.querySelectorAll('.sidebar-nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    if (window.innerWidth <= 768) {
                        body.classList.remove('sidebar-open');
                        sidebarOverlay.classList.remove('show');
                    }
                });
            });
        });

        // Toast helper functions
        function showSuccessToast(message) {
            const toast = document.getElementById('successToast');
            toast.querySelector('.toast-body').textContent = message;
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }

        function showErrorToast(message) {
            const toast = document.getElementById('errorToast');
            toast.querySelector('.toast-body').textContent = message;
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }

        // Smooth scrolling functions
        function scrollToPresensi() {
            const element = document.getElementById('presensi-section');
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        function scrollToHistory() {
            const element = document.getElementById('history-section');
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        // Initialize AOS
        AOS.init();

        // Update current time in header
        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('id-ID', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const currentTimeEl = document.getElementById('currentTime');
            if (currentTimeEl) {
                currentTimeEl.textContent = timeString;
            }
        }

        // Update time every second
        setInterval(updateCurrentTime, 1000);
        updateCurrentTime(); // Initial call
    </script>

    @stack('scripts')
</body>
</html>
