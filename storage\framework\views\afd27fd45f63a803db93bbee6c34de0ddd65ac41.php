<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title><?php echo $__env->yieldContent('title', 'Dashboard'); ?> - RSPPU PPDS RSK Dharmais</title>
    <meta name="description" content="">
    <meta name="keywords" content="">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <!-- Favicons -->
    <link href="<?php echo e(asset('assets/img/dharmais_icon.png')); ?>" rel="icon">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com" rel="preconnect">
    <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Raleway:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

    <!-- Vendor CSS Files -->
    <link href="<?php echo e(asset('assets/vendor/bootstrap/css/bootstrap.min.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('assets/vendor/bootstrap-icons/bootstrap-icons.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('assets/vendor/aos/aos.css')); ?>" rel="stylesheet">

    <!-- Main CSS File -->
    <link href="<?php echo e(asset('assets/css/main.css')); ?>" rel="stylesheet">

    <style>
        /* Medical/Hospital Theme Color Variables */
        :root {
            --medical-primary: #1e3a8a;
            --medical-secondary: #3b82f6;
            --medical-light: #60a5fa;
            --medical-accent: #0ea5e9;
            --medical-bg: #f8fafc;
            --medical-surface: #ffffff;
            --medical-border: #e2e8f0;
            --medical-text: #1e293b;
            --medical-text-light: #64748b;
        }

        body {
            background-color: var(--medical-bg);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            padding-top: 60px !important; /* Account for fixed navbar */
            overflow-x: hidden;
        }

        .navbar-brand img {
            max-height: 40px;
        }

        .bg-gradient-primary {
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
        }

        .card {
            transition: transform 0.2s ease-in-out;
            border: 1px solid var(--medical-border);
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        /* Sidebar Styling with Medical Theme - Truly Fixed Position */
        .sidebar-container {
            position: fixed !important;
            top: 60px;
            left: 0;
            width: 280px;
            height: calc(100vh - 60px);
            z-index: 1000;
            transition: transform 0.3s ease;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .sidebar {
            width: 280px; /* Fixed width to prevent horizontal scroll */
            height: 100%;
            background: linear-gradient(180deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
            padding: 1.5rem;
            overflow-y: auto;
            overflow-x: hidden;
            margin: 0;
            border: none;
            display: flex;
            flex-direction: column;
            position: relative;
            box-sizing: border-box; /* Include padding in width calculation */
        }

        /* Sidebar Profile Section */
        .sidebar-profile {
            flex-shrink: 0;
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid rgba(255,255,255,0.2);
        }

        .profile-avatar i {
            font-size: 3rem;
        }

        .profile-name {
            font-weight: 600;
            font-size: 1rem;
        }

        .profile-role {
            font-size: 0.875rem;
        }

        /* Sidebar Navigation */
        .sidebar-nav {
            flex: 1;
            overflow-y: auto;
        }

        .sidebar-nav .nav {
            padding: 0;
            margin: 0;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.9);
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            margin: 0.25rem 0;
            transition: all 0.3s ease;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            text-decoration: none;
            font-weight: 500;
            white-space: nowrap; /* Prevent text wrapping */
            overflow: hidden; /* Hide overflow */
            text-overflow: ellipsis; /* Show ellipsis for long text */
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateX(5px);
            border-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
            margin-right: 0.75rem;
            font-size: 1.1rem;
        }

        .sidebar .nav-link span {
            font-size: 0.95rem;
        }

        /* Sidebar Footer */
        .sidebar-footer {
            flex-shrink: 0;
            border-top: 1px solid rgba(255,255,255,0.1);
            padding-top: 1rem;
        }

        /* Content area adjustment */
        .main-content {
            margin-left: 280px;
            transition: margin-left 0.3s ease;
            min-height: calc(100vh - 60px);
            padding-top: 0;
            display: flex;
            flex-direction: column;
            width: calc(100% - 280px); /* Prevent content from extending under sidebar */
            box-sizing: border-box;
        }

        .content-wrapper {
            flex: 1;
            padding: 2rem;
            background-color: var(--medical-bg);
            width: 100%;
            box-sizing: border-box;
        }

        /* Hamburger Menu Button */
        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--medical-primary);
            font-size: 1.5rem;
            padding: 0.5rem;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .sidebar-toggle:hover {
            background-color: var(--medical-border);
            color: var(--medical-secondary);
        }

        /* Sidebar States */
        .sidebar-hidden .sidebar-container {
            transform: translateX(-100%);
        }

        .sidebar-hidden .main-content {
            margin-left: 0;
        }

        .sidebar-hidden .footer {
            margin-left: 0;
            width: 100%;
        }

        /* Sidebar Overlay */
        .sidebar-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            width: 100vw;
            height: calc(100vh - 60px);
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .sidebar-overlay.show {
            display: block;
            opacity: 1;
        }

        /* Responsive Behavior */
        @media (max-width: 768px) {
            .sidebar-container {
                transform: translateX(-100%);
            }

            .sidebar-container.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0 !important;
                width: 100% !important;
            }

            .footer {
                margin-left: 0 !important;
                width: 100% !important;
            }
        }

        @media (min-width: 769px) {
            .sidebar-overlay {
                display: none !important;
            }

            /* Ensure sidebar is always visible on desktop */
            .sidebar-container {
                transform: translateX(0);
            }
        }

        /* Scrollbar styling for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.5);
        }

        #video {
            border: 3px solid var(--medical-border);
            border-radius: 10px;
        }

        .btn-lg {
            padding: 0.75rem 1.5rem;
            font-size: 1.1rem;
            background-color: var(--medical-secondary);
            border-color: var(--medical-secondary);
        }

        .btn-lg:hover {
            background-color: var(--medical-primary);
            border-color: var(--medical-primary);
        }

        .table th {
            border-top: none;
            font-weight: 600;
            color: var(--medical-text);
            background-color: var(--medical-bg);
        }

        .badge {
            font-size: 0.75rem;
        }

        .alert {
            border: none;
            border-radius: 10px;
        }

        .rounded-circle {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Footer Styling */
        .footer {
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
            color: white;
            padding: 2rem 0 1rem 0;
            margin-left: 280px;
            margin-top: 0;
            position: relative;
            z-index: 1;
            transition: margin-left 0.3s ease;
            width: calc(100% - 280px);
            box-sizing: border-box;
        }

        .footer a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer a:hover {
            color: white;
        }

        /* Layout Structure */
        .wrapper {
            min-height: 100vh;
            background-color: var(--medical-bg);
        }

        .main-container {
            display: block;
            position: relative;
        }

        /* Navbar fixed positioning */
        .navbar {
            position: fixed !important;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1100;
            height: 60px;
            margin: 0;
            padding: 0 1rem;
            border: none;
            width: 100%;
            box-sizing: border-box;
        }

        /* Remove default Bootstrap margins/padding that might cause gaps */
        .container-fluid {
            padding-left: 0 !important;
            padding-right: 0 !important;
            margin: 0 !important;
            max-width: none !important;
            width: 100% !important;
        }

        /* Remove any default margins/padding and ensure proper box-sizing */
        * {
            box-sizing: border-box;
        }

        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow-x: hidden; /* Prevent horizontal scroll on body */
        }

        /* Ensure main layout containers don't cause horizontal scroll */
        .wrapper, .main-container {
            width: 100%;
            overflow-x: hidden;
        }

        /* Additional fixes for layout issues */
        .row {
            margin-left: 0 !important;
            margin-right: 0 !important;
        }

        .col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
        .col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
        .col-auto, .col-sm, .col-sm-1, .col-sm-2, .col-sm-3,
        .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8,
        .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-auto,
        .col-md, .col-md-1, .col-md-2, .col-md-3, .col-md-4,
        .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9,
        .col-md-10, .col-md-11, .col-md-12, .col-md-auto,
        .col-lg, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4,
        .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9,
        .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-auto,
        .col-xl, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4,
        .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9,
        .col-xl-10, .col-xl-11, .col-xl-12, .col-xl-auto {
            padding-left: 15px !important;
            padding-right: 15px !important;
        }

        /* Ensure cards and content don't overflow */
        .card, .card-body {
            overflow-x: hidden;
        }

        /* Fix for video element to prevent overflow */
        #video {
            max-width: 100%;
            height: auto;
        }


    </style>
</head>

<body class="wrapper">
    <!-- Navigation -->
    <?php echo $__env->make('layouts.partials.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <!-- Include Sidebar Partial -->
    <?php echo $__env->make('layouts.partials.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <!-- Header Section (optional, for dashboard) -->
    <?php echo $__env->yieldContent('header'); ?>

    <!-- Main Content -->
    <div class="main-content">
        <div class="content-wrapper">
            <?php echo $__env->yieldContent('content'); ?>
        </div>
    </div>

    <!-- Footer -->
    <?php echo $__env->make('layouts.partials.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mb-0">Sedang memproses presensi...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1100;">
        <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header bg-success text-white">
                <i class="bi bi-check-circle me-2"></i>
                <strong class="me-auto">Berhasil</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body"></div>
        </div>

        <div id="errorToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header bg-danger text-white">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <strong class="me-auto">Error</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body"></div>
        </div>
    </div>

    <!-- Vendor JS Files -->
    <script src="<?php echo e(asset('assets/vendor/bootstrap/js/bootstrap.bundle.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/aos/aos.js')); ?>"></script>

    <!-- Main JS File -->
    <script src="<?php echo e(asset('assets/js/main.js')); ?>"></script>

    <script>
        // Sidebar Toggle Functionality
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebarContainer = document.getElementById('sidebarContainer');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            const body = document.body;

            let sidebarVisible = window.innerWidth > 768;

            // Initialize sidebar state
            function initSidebar() {
                if (window.innerWidth <= 768) {
                    body.classList.add('sidebar-hidden');
                    sidebarVisible = false;
                } else {
                    body.classList.remove('sidebar-hidden');
                    sidebarVisible = true;
                }
            }

            // Toggle sidebar
            function toggleSidebar() {
                if (window.innerWidth <= 768) {
                    // Mobile behavior
                    sidebarContainer.classList.toggle('show');
                    sidebarOverlay.classList.toggle('show');
                } else {
                    // Desktop behavior
                    body.classList.toggle('sidebar-hidden');
                    sidebarVisible = !sidebarVisible;
                }
            }

            // Event listeners
            sidebarToggle.addEventListener('click', toggleSidebar);

            // Close sidebar when clicking overlay (mobile)
            sidebarOverlay.addEventListener('click', function() {
                sidebarContainer.classList.remove('show');
                sidebarOverlay.classList.remove('show');
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    // Desktop mode
                    sidebarContainer.classList.remove('show');
                    sidebarOverlay.classList.remove('show');
                    if (!sidebarVisible) {
                        body.classList.add('sidebar-hidden');
                    } else {
                        body.classList.remove('sidebar-hidden');
                    }
                } else {
                    // Mobile mode
                    body.classList.add('sidebar-hidden');
                }
            });

            // Close sidebar when clicking nav links on mobile
            const navLinks = sidebarContainer.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    if (window.innerWidth <= 768) {
                        sidebarContainer.classList.remove('show');
                        sidebarOverlay.classList.remove('show');
                    }
                });
            });

            // Initialize
            initSidebar();
        });

        // Toast helper functions
        function showSuccessToast(message) {
            const toast = document.getElementById('successToast');
            toast.querySelector('.toast-body').textContent = message;
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }

        function showErrorToast(message) {
            const toast = document.getElementById('errorToast');
            toast.querySelector('.toast-body').textContent = message;
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }

        // Smooth scrolling functions
        function scrollToPresensi() {
            document.querySelector('[data-section="presensi"]')?.scrollIntoView({ behavior: 'smooth' });
        }

        function scrollToHistory() {
            document.querySelector('[data-section="history"]')?.scrollIntoView({ behavior: 'smooth' });
        }

        // Initialize AOS
        AOS.init();

        // Auto-refresh time
        setInterval(function() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('id-ID');
            document.querySelectorAll('.current-time').forEach(el => {
                el.textContent = timeString;
            });
        }, 1000);
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\laragon\www\rsppu\resources\views/layouts/dashboard.blade.php ENDPATH**/ ?>