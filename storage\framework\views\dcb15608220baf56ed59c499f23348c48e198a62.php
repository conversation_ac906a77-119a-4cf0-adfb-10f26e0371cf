<?php $__env->startSection('title', 'Dashboard PPDS'); ?>

<?php $__env->startSection('page-header'); ?>
<div class="d-flex justify-content-between align-items-center">
    <div>
        <h1 class="page-title">Selamat Datang, <?php echo e($user->nama_lengkap); ?>!</h1>
        <p class="page-subtitle">Dashboard Presensi PPDS RS Kanker Dharmais</p>
    </div>
    <div class="d-none d-md-block">
        <div class="d-flex align-items-center gap-3">
            <div class="text-end">
                <div class="fw-semibold text-primary-custom"><?php echo e(\Carbon\Carbon::now()->format('l')); ?></div>
                <div class="text-muted-custom"><?php echo e(\Carbon\Carbon::now()->format('d F Y')); ?></div>
            </div>
            <div class="sidebar-avatar" style="width: 50px; height: 50px;">
                <i class="bi bi-calendar-date"></i>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid p-0">
    <!-- Status Cards -->
    <div class="row g-3 mb-4">
        <div class="col-12 col-md-6">
            <div class="dashboard-card h-100">
                <div class="card-body-custom">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="sidebar-avatar me-3" style="width: 60px; height: 60px; background: linear-gradient(135deg, var(--success-color), #34d399);">
                                <i class="bi bi-clock"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1" style="min-width: 0;">
                            <h6 class="card-title mb-2">Status Presensi Hari Ini</h6>
                            <?php if($todayAttendance): ?>
                                <?php if($todayAttendance->jam_masuk && !$todayAttendance->jam_keluar): ?>
                                    <span class="status-badge status-warning">Sudah Masuk</span>
                                    <div class="text-muted-custom mt-2" style="font-size: 0.85rem;">
                                        Masuk: <?php echo e($todayAttendance->jam_masuk->format('H:i')); ?>

                                    </div>
                                <?php elseif($todayAttendance->jam_masuk && $todayAttendance->jam_keluar): ?>
                                    <span class="status-badge status-success">Lengkap</span>
                                    <div class="text-muted-custom mt-2" style="font-size: 0.85rem;">
                                        Masuk: <?php echo e($todayAttendance->jam_masuk->format('H:i')); ?> |
                                        Keluar: <?php echo e($todayAttendance->jam_keluar->format('H:i')); ?>

                                    </div>
                                <?php endif; ?>
                            <?php else: ?>
                                <span class="status-badge status-danger">Belum Presensi</span>
                                <div class="text-muted-custom mt-2" style="font-size: 0.85rem;">
                                    Silakan lakukan presensi masuk
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6">
            <div class="dashboard-card h-100">
                <div class="card-body-custom">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="sidebar-avatar me-3" style="width: 60px; height: 60px; background: linear-gradient(135deg, var(--info-color), #38bdf8);">
                                <i class="bi bi-geo-alt"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1" style="min-width: 0;">
                            <h6 class="card-title mb-2">Lokasi Saat Ini</h6>
                            <div id="currentLocation" class="text-muted-custom text-truncate" style="font-size: 0.9rem;">
                                Mendeteksi lokasi...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Presensi Section -->
    <div class="row g-3 mb-4" id="presensi-section">
        <div class="col-12">
            <div class="dashboard-card">
                <div class="card-header-custom">
                    <h5 class="card-title">
                        <i class="bi bi-camera me-2"></i>Presensi dengan Kamera
                    </h5>
                </div>
                <div class="card-body-custom">
                    <div class="row g-3">
                        <div class="col-12 col-lg-6">
                            <div class="camera-container text-center">
                                <video id="video" width="100%" height="300" autoplay class="rounded-custom"></video>
                                <canvas id="canvas" style="display: none;"></canvas>
                                <div class="mt-3">
                                    <div class="d-flex gap-2 justify-content-center flex-wrap">
                                        <button id="startCamera" class="btn btn-outline-custom">
                                            <i class="bi bi-camera-video me-1 d-none d-sm-inline"></i>
                                            <span class="d-none d-sm-inline">Aktifkan </span>Kamera
                                        </button>
                                        <button id="capturePhoto" class="btn btn-primary-custom" disabled>
                                            <i class="bi bi-camera me-1 d-none d-sm-inline"></i>
                                            Ambil Foto
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6">
                            <div id="previewSection" style="display: none;">
                                <h6 class="mb-3">Preview Foto:</h6>
                                <div class="camera-container text-center mb-3">
                                    <img id="photoPreview" class="img-fluid rounded-custom" style="max-height: 300px; max-width: 100%;">
                                </div>
                                <div class="d-grid gap-2">
                                    <?php if(!$todayAttendance): ?>
                                        <button id="checkInBtn" class="btn btn-primary-custom btn-lg">
                                            <i class="bi bi-box-arrow-in-right me-1 d-none d-sm-inline"></i>
                                            Presensi Masuk
                                        </button>
                                    <?php elseif($todayAttendance && !$todayAttendance->jam_keluar): ?>
                                        <button id="checkOutBtn" class="btn btn-lg" style="background: var(--warning-color); color: white; font-weight: 500;">
                                            <i class="bi bi-box-arrow-left me-1 d-none d-sm-inline"></i>
                                            Presensi Keluar
                                        </button>
                                    <?php else: ?>
                                        <div class="alert alert-success border-0 rounded-custom">
                                            <i class="bi bi-check-circle me-2"></i>
                                            <span class="d-none d-sm-inline">Presensi hari ini sudah </span>Lengkap
                                        </div>
                                    <?php endif; ?>
                                    <button id="retakePhoto" class="btn btn-outline-custom">
                                        <i class="bi bi-arrow-clockwise me-1 d-none d-sm-inline"></i>
                                        Ambil Ulang
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Riwayat Presensi Section -->
    <div class="row g-3" id="history-section">
        <div class="col-12">
            <div class="dashboard-card">
                <div class="card-header-custom">
                    <h5 class="card-title">
                        <i class="bi bi-clock-history me-2"></i>Riwayat Presensi
                    </h5>
                </div>
                <div class="card-body-custom">
                    <?php if($attendanceHistory->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-custom mb-0">
                                <thead>
                                    <tr>
                                        <th style="min-width: 80px;">Tanggal</th>
                                        <th style="min-width: 70px;" class="text-center">Masuk</th>
                                        <th style="min-width: 70px;" class="text-center">Keluar</th>
                                        <th style="min-width: 80px;" class="text-center">Status</th>
                                        <th style="min-width: 60px;" class="text-center">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $attendanceHistory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attendance): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div class="fw-semibold" style="font-size: 0.9rem;">
                                                <?php echo e($attendance->tanggal->format('d/m/Y')); ?>

                                            </div>
                                            <div class="text-muted-custom d-none d-md-block" style="font-size: 0.75rem;">
                                                <?php echo e($attendance->tanggal->format('D')); ?>

                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <?php if($attendance->jam_masuk): ?>
                                                <span class="status-badge status-success"><?php echo e($attendance->jam_masuk->format('H:i')); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted-custom">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-center">
                                            <?php if($attendance->jam_keluar): ?>
                                                <span class="status-badge status-warning"><?php echo e($attendance->jam_keluar->format('H:i')); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted-custom">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-center">
                                            <?php if($attendance->jam_masuk && $attendance->jam_keluar): ?>
                                                <span class="status-badge status-success d-none d-sm-inline">Lengkap</span>
                                                <span class="status-badge status-success d-inline d-sm-none">✓</span>
                                            <?php elseif($attendance->jam_masuk): ?>
                                                <span class="status-badge status-warning d-none d-sm-inline">Masuk</span>
                                                <span class="status-badge status-warning d-inline d-sm-none">½</span>
                                            <?php else: ?>
                                                <span class="status-badge status-danger d-none d-sm-inline">Tidak</span>
                                                <span class="status-badge status-danger d-inline d-sm-none">✗</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-center">
                                            <button class="btn btn-outline-custom btn-sm" onclick="viewDetail(<?php echo e($attendance->id); ?>)" style="padding: 0.25rem 0.5rem;">
                                                <i class="bi bi-eye"></i>
                                                <span class="d-none d-md-inline ms-1">Detail</span>
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <div class="sidebar-avatar mx-auto mb-3" style="width: 60px; height: 60px; background: var(--bg-tertiary); color: var(--text-muted);">
                                <i class="bi bi-inbox"></i>
                            </div>
                            <h6 class="text-muted-custom">Belum ada riwayat</h6>
                            <p class="text-muted-custom mb-0 small">Riwayat akan muncul setelah presensi</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
let stream = null;
let currentLocation = null;
let capturedPhoto = null;

// Get current location
function getCurrentLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                currentLocation = {
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude
                };

                // Display coordinates directly (no external API needed)
                document.getElementById('currentLocation').textContent =
                    `${currentLocation.latitude.toFixed(6)}, ${currentLocation.longitude.toFixed(6)}`;
            },
            function(error) {
                let errorMessage = 'Lokasi tidak dapat dideteksi';
                switch(error.code) {
                    case error.PERMISSION_DENIED:
                        errorMessage = 'Akses lokasi ditolak oleh user';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        errorMessage = 'Informasi lokasi tidak tersedia';
                        break;
                    case error.TIMEOUT:
                        errorMessage = 'Request lokasi timeout';
                        break;
                }
                document.getElementById('currentLocation').textContent = errorMessage;
                console.error('Error getting location:', error);
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000
            }
        );
    } else {
        document.getElementById('currentLocation').textContent = 'Geolocation tidak didukung browser';
    }
}

// Start camera
document.getElementById('startCamera').addEventListener('click', function() {
    const video = document.getElementById('video');

    navigator.mediaDevices.getUserMedia({ video: true })
        .then(function(mediaStream) {
            stream = mediaStream;
            video.srcObject = stream;

            document.getElementById('startCamera').disabled = true;
            document.getElementById('capturePhoto').disabled = false;
        })
        .catch(function(error) {
            console.error('Error accessing camera:', error);
            alert('Tidak dapat mengakses kamera. Pastikan Anda memberikan izin akses kamera.');
        });
});

// Capture photo
document.getElementById('capturePhoto').addEventListener('click', function() {
    const video = document.getElementById('video');
    const canvas = document.getElementById('canvas');
    const context = canvas.getContext('2d');

    // Validate video is ready
    if (video.videoWidth === 0 || video.videoHeight === 0) {
        alert('Kamera belum siap. Silakan tunggu sebentar.');
        return;
    }

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    context.drawImage(video, 0, 0);

    // Convert to JPEG with quality compression
    capturedPhoto = canvas.toDataURL('image/jpeg', 0.8);

    // Validate photo size (max 2MB)
    const photoSize = Math.round((capturedPhoto.length * 3/4) / 1024 / 1024);
    if (photoSize > 2) {
        alert('Ukuran foto terlalu besar. Silakan coba lagi.');
        return;
    }

    // Show preview
    document.getElementById('photoPreview').src = capturedPhoto;
    document.getElementById('previewSection').style.display = 'block';

    // Stop camera
    if (stream) {
        stream.getTracks().forEach(track => track.stop());
        stream = null;
    }

    document.getElementById('startCamera').disabled = false;
    document.getElementById('capturePhoto').disabled = true;
});

// Retake photo
document.getElementById('retakePhoto').addEventListener('click', function() {
    document.getElementById('previewSection').style.display = 'none';
    capturedPhoto = null;
    document.getElementById('startCamera').click();
});

// Check in
document.getElementById('checkInBtn')?.addEventListener('click', function() {
    if (!capturedPhoto) {
        alert('Silakan ambil foto terlebih dahulu');
        return;
    }

    if (!currentLocation) {
        alert('Lokasi belum terdeteksi. Silakan tunggu sebentar.');
        return;
    }

    // Disable button to prevent double submission
    this.disabled = true;
    this.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Memproses...';

    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    loadingModal.show();

    const formData = new FormData();
    formData.append('foto', capturedPhoto);
    formData.append('latitude', currentLocation.latitude);
    formData.append('longitude', currentLocation.longitude);
    formData.append('alamat', document.getElementById('currentLocation').textContent);

    fetch('<?php echo e(route("presensi.masuk")); ?>', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        loadingModal.hide();
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert(data.message);
            // Re-enable button
            document.getElementById('checkInBtn').disabled = false;
            document.getElementById('checkInBtn').innerHTML = '<i class="bi bi-box-arrow-in-right me-2"></i>Presensi Masuk';
        }
    })
    .catch(error => {
        loadingModal.hide();
        console.error('Error:', error);
        alert('Terjadi kesalahan sistem');
        // Re-enable button
        document.getElementById('checkInBtn').disabled = false;
        document.getElementById('checkInBtn').innerHTML = '<i class="bi bi-box-arrow-in-right me-2"></i>Presensi Masuk';
    });
});

// Check out
document.getElementById('checkOutBtn')?.addEventListener('click', function() {
    if (!capturedPhoto) {
        alert('Silakan ambil foto terlebih dahulu');
        return;
    }

    if (!currentLocation) {
        alert('Lokasi belum terdeteksi. Silakan tunggu sebentar.');
        return;
    }

    // Disable button to prevent double submission
    this.disabled = true;
    this.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Memproses...';

    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    loadingModal.show();

    const formData = new FormData();
    formData.append('foto', capturedPhoto);
    formData.append('latitude', currentLocation.latitude);
    formData.append('longitude', currentLocation.longitude);
    formData.append('alamat', document.getElementById('currentLocation').textContent);

    fetch('<?php echo e(route("presensi.keluar")); ?>', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        loadingModal.hide();
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert(data.message);
            // Re-enable button
            document.getElementById('checkOutBtn').disabled = false;
            document.getElementById('checkOutBtn').innerHTML = '<i class="bi bi-box-arrow-left me-2"></i>Presensi Keluar';
        }
    })
    .catch(error => {
        loadingModal.hide();
        console.error('Error:', error);
        alert('Terjadi kesalahan sistem');
        // Re-enable button
        document.getElementById('checkOutBtn').disabled = false;
        document.getElementById('checkOutBtn').innerHTML = '<i class="bi bi-box-arrow-left me-2"></i>Presensi Keluar';
    });
});

// View detail function
function viewDetail(id) {
    // Create modal for detail view
    const modalHtml = `
        <div class="modal fade" id="detailModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Detail Presensi</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Memuat detail presensi...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('detailModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('detailModal'));
    modal.show();

    // Fetch actual detail data
    fetch(`/presensi/detail/${id}`, {
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const detail = data.data;
            document.querySelector('#detailModal .modal-body').innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Informasi Presensi</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Tanggal:</strong></td><td>${detail.tanggal}</td></tr>
                            <tr><td><strong>Jam Masuk:</strong></td><td>${detail.jam_masuk || '-'}</td></tr>
                            <tr><td><strong>Jam Keluar:</strong></td><td>${detail.jam_keluar || '-'}</td></tr>
                            <tr><td><strong>Status:</strong></td><td><span class="badge bg-${detail.status === 'keluar' ? 'success' : 'warning'}">${detail.status}</span></td></tr>
                        </table>

                        <h6 class="mt-3">Lokasi</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Lokasi Masuk:</strong></td><td>${detail.lokasi_masuk || '-'}</td></tr>
                            <tr><td><strong>Alamat Masuk:</strong></td><td>${detail.alamat_masuk || '-'}</td></tr>
                            ${detail.lokasi_keluar ? `<tr><td><strong>Lokasi Keluar:</strong></td><td>${detail.lokasi_keluar}</td></tr>` : ''}
                            ${detail.alamat_keluar ? `<tr><td><strong>Alamat Keluar:</strong></td><td>${detail.alamat_keluar}</td></tr>` : ''}
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Foto Presensi</h6>
                        ${detail.foto_masuk ? `
                            <div class="mb-3">
                                <label class="form-label"><strong>Foto Masuk:</strong></label>
                                <img src="${detail.foto_masuk}" class="img-fluid rounded" style="max-height: 200px;">
                            </div>
                        ` : ''}
                        ${detail.foto_keluar ? `
                            <div class="mb-3">
                                <label class="form-label"><strong>Foto Keluar:</strong></label>
                                <img src="${detail.foto_keluar}" class="img-fluid rounded" style="max-height: 200px;">
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        } else {
            document.querySelector('#detailModal .modal-body').innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    ${data.message}
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.querySelector('#detailModal .modal-body').innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle me-2"></i>
                Terjadi kesalahan saat memuat detail presensi
            </div>
        `;
    });
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    getCurrentLocation();
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\rsppu\resources\views/dashboard.blade.php ENDPATH**/ ?>